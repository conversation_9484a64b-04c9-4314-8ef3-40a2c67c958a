import React from 'react';
import {Alert} from 'react-native';
import {userEvent, screen, waitFor, render} from '../../testUtils';
import MomentEntry from '../MomentEntry';
import {availableMomentTypes} from '../../__mocks__/availableMomentTypes';
import Meteor from 'react-native-meteor';

// Mock react-i18next with actual translations
jest.mock('react-i18next', () => ({
  useTranslation: () => {
    const enTranslations = require('../../localization/locales/en/translation.json');
    const enAlerts = require('../../localization/locales/en/alerts.json');
    const enBusiness = require('../../localization/locales/en/business.json');

    return {
      t: (key, options = {}) => {
        const { defaultValue } = options;
        const keys = key.split('.');
        let value = enTranslations;

        // Try to find the key in translation files
        for (const k of keys) {
          if (value && typeof value === 'object' && k in value) {
            value = value[k];
          } else {
            value = undefined;
            break;
          }
        }

        // If not found in main translations, try business translations
        if (value === undefined) {
          value = enBusiness;
          for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
              value = value[k];
            } else {
              value = undefined;
              break;
            }
          }
        }

        // If not found in business, try alerts
        if (value === undefined) {
          value = enAlerts;
          for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
              value = value[k];
            } else {
              value = undefined;
              break;
            }
          }
        }

        return value !== undefined ? value : (defaultValue || key);
      },
      i18n: {
        changeLanguage: jest.fn(),
        language: 'en',
      },
    };
  },
  withTranslation: () => (Component) => {
    const WrappedComponent = (props) => {
      const { t } = require('react-i18next').useTranslation();
      return <Component {...props} t={t} />;
    };
    WrappedComponent.displayName = `withTranslation(${Component.displayName || Component.name})`;
    return WrappedComponent;
  },
}));

jest.mock('../../shared/MomentDefinitions', () => {
  const standardMomentTypes = {
    comment: {
      prettyName: 'Comment',
      icon: {uri: 'mt_comment_icon_sm'},
      listIcon: {uri: 'mt_comment_icon_purple'},
      hideComment: false,
      hideTimePicker: false,
      hideDatePicker: false,
    },
    checkin: {
      prettyName: 'Check In',
      icon: {uri: 'mt_notification_icon_sm'},
      listIcon: {uri: 'mt_notification_icon_purple'},
      disableTagging: true,
    },
    checkout: {
      prettyName: 'Check Out',
      icon: {uri: 'mt_notification_icon_sm'},
      listIcon: {uri: 'mt_notification_icon_purple'},
      disableTagging: true,
    },
    move: {
      prettyName: 'Move',
      icon: {uri: 'mt_notification_icon_sm'},
      listIcon: {uri: 'mt_notification_icon_purple'},
      disableTagging: true,
    },
    potty: {
      prettyName: 'Potty',
      icon: {uri: 'mt_potty_icon_sm'},
      listIcon: {uri: 'mt_potty_icon_purple'},
      fields: [
        {
          dataId: 'pottyType',
          label: 'Type',
          fieldType: 'buttons',
          fieldValues: [
            {fieldValue: 'Wet'},
            {fieldValue: 'BM'},
            {fieldValue: 'Wet+BM'},
            {fieldValue: 'Dry'},
          ],
        },
        {
          dataId: 'pottyTypeContinence',
          label: 'Continence',
          fieldType: 'buttons',
          fieldValues: [
            {fieldValue: 'Continent'},
            {fieldValue: 'Incontinent'},
            {fieldValue: 'Refused'},
          ],
          customization: 'moments/potty/showContinence',
        },
        {
          dataId: 'pottyAppliedOintment',
          label: 'Applied Ointment',
          fieldType: 'checkbox',
          languages: ['', 'translationsEnChildCare'],
        },
        {
          dataId: 'pottyTraining',
          label: 'Training',
          fieldType: 'buttons',
          fieldValues: [
            {fieldValue: 'Tried'},
            {fieldValue: 'Successful'},
            {fieldValue: 'Accident'},
          ],
          languages: ['', 'translationsEnChildCare'],
        },
      ],
    },
    offlineFood: {
      prettyName: 'Food',
      icon: {uri: 'mt_food_icon_sm'},
      listIcon: {uri: 'mt_food_icon_purple'},
      fields: [
        {
          dataId: 'foodType',

          label: 'Type',
          fieldType: 'buttons',
          fieldValues: [
            {fieldValue: 'Breakfast'},
            {fieldValue: 'AM Snack'},
            {fieldValue: 'Lunch'},
            {fieldValue: 'PM Snack'},
            {fieldValue: 'Late Snack'},
            {fieldValue: 'Dinner'},
            {fieldValue: 'Bottle', displayRule: 'showBottleType'},
          ],
        },
        {
          dataId: 'foodAmount',
          label: 'Amount',
          fieldType: 'buttons',
          fieldValues: ['All', 'Most', 'Some', 'None', 'Not Offered'],
        },
        {
          dataId: 'foodBottleAmountBreastmilkOffered',
          label: 'Breastmilk Offered',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountBreastmilkConsumed',
          label: 'Breastmilk Consumed',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountFormulaOffered',
          label: 'Formula Offered',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountFormulaConsumed',
          label: 'Formula Consumed',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountMilkOffered',
          label: 'Milk Offered',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountMilkConsumed',
          label: 'Milk Consumed',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
      ],
    },
    food: {
      prettyName: 'Food',
      icon: {uri: 'mt_food_icon_sm'},
      listIcon: {uri: 'mt_food_icon_purple'},
      fields: [
        {
          dataId: 'foodType',

          label: 'Type',
          fieldType: 'buttons',
          fieldValues: [
            {fieldValue: 'Breakfast'},
            {fieldValue: 'AM Snack'},
            {fieldValue: 'Lunch'},
            {fieldValue: 'PM Snack'},
            {fieldValue: 'Late Snack'},
            {fieldValue: 'Dinner'},
            {fieldValue: 'Bottle', displayRule: 'showBottleType'},
            {fieldValue: 'Baby Food', displayRule: 'showBabyFoodType'},
            {fieldValue: 'Cereal', displayRule: 'showCerealType'},
            {fieldValue: 'Tube', customization: 'moments/food/showTube'},
          ],
        },
        {
          dataId: 'foodItems',
          fieldType: 'stateSourcedFieldList',
          stateSource: 'foodMomentItems',
          fieldDefinition: {
            itemName: 'name',
            itemValueName: 'amount',
            fieldType: 'buttons',
            fieldValues: ['All', 'Most', 'Some', 'None', 'Not Offered'],
          },
        },
        {
          dataId: 'foodTubeAmount',
          label: 'Amount',
          fieldType: 'select',
          rightLabel: 'ml',
          fieldValues: [
            '0',
            '60',
            '120',
            '180',
            '240',
            '300',
            '360',
            '420',
            '480',
            '540',
          ],
          displayRule: 'showFoodTubeAmount',
        },
        {
          dataId: 'foodBottleAmountBreastmilkOffered',
          label: 'Breastmilk Offered',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountBreastmilkConsumed',
          label: 'Breastmilk Consumed',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountFormulaOffered',
          label: 'Formula Offered',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountFormulaConsumed',
          label: 'Formula Consumed',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountMilkOffered',
          label: 'Milk Offered',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBottleAmountMilkConsumed',
          label: 'Milk Consumed',
          fieldType: 'amount',
          amountUnits: 'oz',
          displayRule: 'showBottleAmount',
        },
        {
          dataId: 'foodBabyFoodType',
          label: 'Food Type',
          fieldType: 'buttons',
          fieldValues: ['Fruit', 'Vegetable'],
          displayRule: 'showBabyFoodAmount',
        },
        {
          dataId: 'foodBottleAmountBabyFoodConsumed',
          label: 'Amount Eaten',
          fieldType: 'amount',
          amountUnits: 'selectedBabyFoodUnits',
          displayRule: 'showBabyFoodAmount',
        },
        {
          dataId: 'foodBottleAmountCerealConsumed',
          label: 'Amount Eaten',
          fieldType: 'amount',
          amountUnits: 'selectedBabyCerealUnits',
          displayRule: 'showCerealAmount',
        },
        {
          dataId: 'foodAmountPercent',
          label: 'Amount',
          fieldType: 'select',
          fieldValues: [
            '100%',
            '90%',
            '80%',
            '70%',
            '60%',
            '50%',
            '40%',
            '30%',
            '20%',
            '10%',
            '0%',
          ],
          displayRule: 'showFoodAmountPercent',
        },
        {
          dataId: 'foodAmount',
          label: 'Amount',
          fieldType: 'buttons',
          fieldValues: ['All', 'Most', 'Some', 'None', 'Not Offered'],
          displayRule: 'showFoodAmount',
        },
      ],
    },
    sleep: {
      prettyName: 'Sleep',
      icon: {uri: 'mt_sleep_icon_sm'},
      listIcon: {uri: 'mt_sleep_icon_purple'},
      hideTimePicker: true,
      fields: [
        {
          dataId: 'time',
          label: 'Start Time',
          fieldType: 'timePicker',
        },
        {
          dataId: 'endTime',
          label: 'End Time',
          fieldType: 'timePicker',
        },
        {
          dataId: 'sleepDidNotSleep',
          label: 'Did Not Sleep',
          fieldType: 'checkbox',
        },
        {
          dataId: 'sleepSatQuietly',
          label: 'Sat Quietly',
          fieldType: 'checkbox',
        },
      ],
    },
    activity: {
      prettyName: 'Activity',
      icon: {uri: 'mt_activity_icon_sm'},
      listIcon: {uri: 'mt_activity_icon_purple'},
      fields: [
        {
          dataId: 'activityEngagement',
          label: 'Engagement',
          fieldType: 'buttons',
          fieldValues: ['Active', 'Passive', 'Refused', 'Out of Center'],
          displayRule: 'showActivityEngagement',
        },
        {
          dataId: 'activityType',
          label: 'Type',
          fieldType: 'select',
          fieldValuesQuery: 'activityTypes',
        },
      ],
    },
    mood: {
      prettyName: 'Mood',
      icon: {uri: 'mt_mood_icon_sm'},
      listIcon: {uri: 'mt_mood_icon_purple'},
      fields: [
        {
          dataId: 'moodLevel',
          label: 'Mood',
          fieldType: 'buttons',
          fieldValues: [
            {fieldValue: 'Happy', fieldIcon: 'smile-o'},
            {fieldValue: 'SoSo', fieldIcon: 'meh-o'},
            {fieldValue: 'Sad', fieldIcon: 'frown-o'},
          ],
        },
      ],
    },
    illness: {
      prettyName: 'Illness',
      icon: {uri: 'mt_illness_icon_sm'},
      listIcon: {uri: 'mt_illness_icon_purple'},
      fields: [
        {
          dataId: 'illnessSymptoms',
          label: 'Symptoms Observed',
          fieldType: 'select',
          fieldValuesQuery: 'illnessSymptoms',
        },
      ],
    },
    supplies: {
      prettyName: 'Supplies',
      icon: {uri: 'mt_supplies_icon_sm'},
      listIcon: {uri: 'mt_supplies_icon_purple'},
      fields: [
        {
          dataId: 'supplyType',
          label: 'Type',
          fieldType: 'select',
          fieldValuesQuery: 'supplyTypes',
        },
      ],
    },
    learning: {
      prettyName: 'Learning',
      icon: {uri: 'mt_learning_icon_sm'},
      listIcon: {uri: 'mt_learning_icon_purple'},
      fields: [
        {
          dataId: 'learningCurriculumId',
          label: 'Curriculum',
          fieldType: 'select',
          fieldValuesQuery: 'curriculums',
        },
        {
          dataId: 'learningType',
          label: 'Type',
          fieldType: 'select',
          fieldValuesQuery: 'curriculumTypes',
        },
      ],
    },
    portfolio: {
      prettyName: 'Portfolio',
      icon: {uri: 'mt_portfolio_icon_sm'},
      listIcon: {uri: 'mt_portfolio_icon_purple'},
      fields: [
        {
          dataId: 'SelectMilestoneId',
          label: 'Select a milestone',
          fieldType: 'checkbox',
        },
        {
          dataId: 'portfolioAgeRangeId',
          savedValue: 'portfolioCurriculum',
          savedValueLabel: 'headline',
          label: 'Age Range',
          fieldType: 'select',
          fieldValuesQuery: 'portfolioAgeRange',
          editLocked: true,
        },
        {
          dataId: 'portfolioCurriculumId',
          savedValue: 'portfolioCurriculum',
          savedValueLabel: 'headline',
          label: 'Activity',
          fieldType: 'select',
          fieldValuesQuery: 'curriculums',
          editLocked: true,
        },
        {
          dataId: 'portfolioAssessments',
          label: 'Observations',
          fieldType: 'assessments',
        },
      ],
    },
    incident: {
      prettyName: 'Incident',
      icon: {uri: 'mt_incident_icon_sm'},
      listIcon: {uri: 'mt_incident_icon_purple'},
      fields: [
        {
          dataId: 'incidentNature',
          label: 'Nature of Incident',
          fieldType: 'string',
          customization: 'moments/incident/extraIncidentFields',
        },
        {
          dataId: 'incidentActionTaken',
          label: 'Action Taken',
          fieldType: 'string',
          customization: 'moments/incident/extraIncidentFields',
        },
        {
          dataId: 'incidentLocation',
          label: 'Incident Location',
          fieldType: 'string',
          customization: 'moments/incident/extraIncidentFields',
        },
      ],
    },
    alert: {
      prettyName: 'Notification',
      icon: {uri: 'mt_notification_icon_sm'},
      listIcon: {uri: 'mt_notification_icon_purple'},
      validationRules: ['alertValidation', 'smsTextValidation'],
      fields: [
        {
          dataId: 'tagOnlyCheckins',
          label: 'Include only if checked in now',
          fieldType: 'checkbox',
        },
        {
          dataId: 'tagEntireOrg',
          label: 'Tag everyone (admins, staff, families)',
          fieldType: 'checkbox',
        },
        {
          dataId: 'alertSendTypeEmail',
          label: 'Send via Email',
          fieldType: 'checkbox',
        },
        {
          dataId: 'alertSendTypeText',
          label: 'Send via SMS (text)',
          fieldType: 'checkbox',
        },
        {
          dataId: 'alertSendTypePush',
          label: 'Send via Push (app)',
          fieldType: 'checkbox',
        },
      ],
    },
    ouch: {
      prettyName: 'Ouch',
      icon: {uri: 'mt_ouch_icon_sm'},
      listIcon: {uri: 'mt_ouch_icon_purple'},
      fields: [
        {
          dataId: 'ouchDescription',
          label: 'Description',
          fieldType: 'text',
        },
        {
          dataId: 'ouchCare',
          label: 'Care Provided',
          fieldType: 'string',
        },
        {
          dataId: 'ouchContactedParent',
          label: 'Contacted Parent',
          fieldType: 'buttons',
          fieldValues: ['Phone', 'Email', 'In Person', 'Did Not Notify'],
        },
        {
          dataId: 'ouchCalledParentTime',
          label: 'Time',
          fieldType: 'timePicker',
        },
        {
          dataId: 'ouchContactedDoctor',
          label: 'Contacted Doctor',
          fieldType: 'buttons',
          fieldValues: ['Phone', 'Email', 'In Person', 'Did Not Notify'],
        },
        {
          dataId: 'ouchCalledDoctorTime',
          label: 'Time',
          fieldType: 'timePicker',
        },
        {
          dataId: 'ouchNurseNotified',
          label: 'Nurse Notified',
          fieldType: 'checkbox',
        },
        {
          dataId: 'ouchProfessionalMedication',
          label: 'Professional Medication Necessary?',
          fieldType: 'checkbox',
        },
      ],
    },
    medical: {
      prettyName: 'Medical',
      icon: {uri: 'mt_medical_icon_sm'},
      listIcon: {uri: 'mt_medical_icon_purple'},
      fields: [
        {
          dataId: 'medicalMedicineType',
          label: 'Type',
          fieldType: 'select',
          fieldValuesQuery: 'medicineTypes',
          displayRule: 'medicalAllowFreeEntry',
        },
        {
          dataId: 'medicalMedicationName',
          label: 'Medication Name',
          fieldType: 'string',
          displayRule: 'medicalAllowFreeEntry',
        },
        {
          dataId: 'medicalDoctorName',
          label: 'Doctor Name',
          fieldType: 'string',
          displayRule: 'medicalAllowFreeEntry',
        },
        {
          dataId: 'medicalMedicationAmount',
          label: 'Doctor Name',
          fieldType: 'string',
          displayRule: 'medicalAllowFreeEntry',
        },
        {
          dataId: 'medicalMedicationId',
          label: 'Medication',
          fieldType: 'select',
          fieldValuesQuery: 'medications',
          customization: 'moments/medical/useProfileMedications',
          editLocked: true,
          savedValue: 'medicalMedication',
          savedValueLabel: 'medicationDescription',
          validation: [
            {
              mandatory: true,
              message: 'Please select a Medication',
            },
          ],
        },
        {
          dataId: 'medicalAdministeredBy',
          label: 'Administered By',
          fieldType: 'select',
          fieldValuesQuery: 'staffPeople',
          editLocked: true,
          savedValue: 'medicalAdministeredByPerson',
          savedValueLabel: 'fullName',
          validation: [
            {
              mandatory: true,
              message: 'Please select a Administered By',
            },
          ],
        },
      ],
    },
  };
  return class MomentDefinitions {
    constructor(momentType, options = {}) {
      this._momentType = momentType;
      this.options = options;
      this._momentDefinition = standardMomentTypes[momentType];
    }

    getDefinition() {
      return this._momentDefinition;
    }
    static availableMomentTypes() {
      return [
        {
          prettyName: 'Comment',
          icon: {uri: 'mt_comment_icon_sm'},
          listIcon: {uri: 'mt_comment_icon_purple'},
          fields: [],
          hideComment: false,
          hideTimePicker: false,
          hideDatePicker: false,
          name: 'Comment',
          type: 'comment',
        },
        {
          prettyName: 'Potty',
          icon: {uri: 'mt_potty_icon_sm'},
          listIcon: {uri: 'mt_potty_icon_purple'},
          fields: [
            {
              dataId: 'pottyType',
              label: 'Type',
              fieldType: 'buttons',
              fieldValues: [
                {fieldValue: 'Wet'},
                {fieldValue: 'BM'},
                {fieldValue: 'Wet+BM'},
                {fieldValue: 'Dry'},
              ],
            },
            {
              dataId: 'pottyTypeContinence',
              label: 'Continence',
              fieldType: 'buttons',
              fieldValues: [
                {fieldValue: 'Continent'},
                {fieldValue: 'Incontinent'},
                {fieldValue: 'Refused'},
              ],
              customization: 'moments/potty/showContinence',
            },
            {
              dataId: 'pottyAppliedOintment',
              label: 'Applied Ointment',
              fieldType: 'checkbox',
              languages: ['', 'translationsEnChildCare'],
            },
            {
              dataId: 'pottyTraining',
              label: 'Training',
              fieldType: 'buttons',
              fieldValues: [
                {fieldValue: 'Tried'},
                {fieldValue: 'Successful'},
                {fieldValue: 'Accident'},
              ],
              languages: ['', 'translationsEnChildCare'],
            },
          ],
          name: 'Potty',
          type: 'potty',
          adminOnly: false,
        },
        {
          prettyName: 'Food',
          icon: {uri: 'mt_food_icon_sm'},
          listIcon: {uri: 'mt_food_icon_purple'},
          fields: [
            {
              dataId: 'foodType',
              label: 'Type',
              fieldType: 'buttons',
              fieldValues: [
                {fieldValue: 'Breakfast'},
                {fieldValue: 'AM Snack'},
                {fieldValue: 'Lunch'},
                {fieldValue: 'PM Snack'},
                {fieldValue: 'Late Snack'},
                {fieldValue: 'Dinner'},
                {fieldValue: 'Bottle', displayRule: 'showBottleType'},
                {fieldValue: 'Baby Food', displayRule: 'showBabyFoodType'},
                {fieldValue: 'Cereal', displayRule: 'showCerealType'},
                {fieldValue: 'Tube', customization: 'moments/food/showTube'},
              ],
            },
            {
              dataId: 'foodItems',
              fieldType: 'stateSourcedFieldList',
              stateSource: 'foodMomentItems',
              fieldDefinition: {
                itemName: 'name',
                itemValueName: 'amount',
                fieldType: 'buttons',
                fieldValues: ['All', 'Most', 'Some', 'None', 'Not Offered'],
              },
            },
            {
              dataId: 'foodTubeAmount',
              label: 'Amount',
              fieldType: 'select',
              rightLabel: 'ml',
              fieldValues: [
                '0',
                '60',
                '120',
                '180',
                '240',
                '300',
                '360',
                '420',
                '480',
                '540',
              ],
              displayRule: 'showFoodTubeAmount',
            },
            {
              dataId: 'foodBottleAmountBreastmilkOffered',
              label: 'Breastmilk Offered',
              fieldType: 'amount',
              amountUnits: 'oz',
              displayRule: 'showBottleAmount',
            },
            {
              dataId: 'foodBottleAmountBreastmilkConsumed',
              label: 'Breastmilk Consumed',
              fieldType: 'amount',
              amountUnits: 'oz',
              displayRule: 'showBottleAmount',
            },
            {
              dataId: 'foodBottleAmountFormulaOffered',
              label: 'Formula Offered',
              fieldType: 'amount',
              amountUnits: 'oz',
              displayRule: 'showBottleAmount',
            },
            {
              dataId: 'foodBottleAmountFormulaConsumed',
              label: 'Formula Consumed',
              fieldType: 'amount',
              amountUnits: 'oz',
              displayRule: 'showBottleAmount',
            },
            {
              dataId: 'foodBottleAmountMilkOffered',
              label: 'Milk Offered',
              fieldType: 'amount',
              amountUnits: 'oz',
              displayRule: 'showBottleAmount',
            },
            {
              dataId: 'foodBottleAmountMilkConsumed',
              label: 'Milk Consumed',
              fieldType: 'amount',
              amountUnits: 'oz',
              displayRule: 'showBottleAmount',
            },
            {
              dataId: 'foodBabyFoodType',
              label: 'Food Type',
              fieldType: 'buttons',
              fieldValues: ['Fruit', 'Vegetable'],
              displayRule: 'showBabyFoodAmount',
            },
            {
              dataId: 'foodBottleAmountBabyFoodConsumed',
              label: 'Amount Eaten',
              fieldType: 'amount',
              amountUnits: 'selectedBabyFoodUnits',
              displayRule: 'showBabyFoodAmount',
            },
            {
              dataId: 'foodBottleAmountCerealConsumed',
              label: 'Amount Eaten',
              fieldType: 'amount',
              amountUnits: 'selectedBabyCerealUnits',
              displayRule: 'showCerealAmount',
            },
            {
              dataId: 'foodAmountPercent',
              label: 'Amount',
              fieldType: 'select',
              fieldValues: [
                '100%',
                '90%',
                '80%',
                '70%',
                '60%',
                '50%',
                '40%',
                '30%',
                '20%',
                '10%',
                '0%',
              ],
              displayRule: 'showFoodAmountPercent',
            },
            {
              dataId: 'foodAmount',
              label: 'Amount',
              fieldType: 'buttons',
              fieldValues: ['All', 'Most', 'Some', 'None', 'Not Offered'],
              displayRule: 'showFoodAmount',
            },
          ],
          name: 'Food',
          type: 'food',
          adminOnly: false,
        },
        {
          prettyName: 'Sleep',
          icon: {uri: 'mt_sleep_icon_sm'},
          listIcon: {uri: 'mt_sleep_icon_purple'},
          hideTimePicker: true,
          fields: [
            {dataId: 'time', label: 'Start Time', fieldType: 'timePicker'},
            {dataId: 'endTime', label: 'End Time', fieldType: 'timePicker'},
            {
              dataId: 'sleepDidNotSleep',
              label: 'Did Not Sleep',
              fieldType: 'checkbox',
            },
            {
              dataId: 'sleepSatQuietly',
              label: 'Sat Quietly',
              fieldType: 'checkbox',
            },
          ],
          name: 'Sleep',
          type: 'sleep',
          adminOnly: false,
        },
        {
          prettyName: 'Activity',
          icon: {uri: 'mt_activity_icon_sm'},
          listIcon: {uri: 'mt_activity_icon_purple'},
          fields: [
            {
              dataId: 'activityEngagement',
              label: 'Engagement',
              fieldType: 'buttons',
              fieldValues: ['Active', 'Passive', 'Refused', 'Out of Center'],
              displayRule: 'showActivityEngagement',
            },
            {
              dataId: 'activityType',
              label: 'Type',
              fieldType: 'select',
              fieldValuesQuery: 'activityTypes',
            },
          ],
          name: 'Activity',
          type: 'activity',
          adminOnly: false,
        },
        {
          prettyName: 'Medical',
          icon: {uri: 'mt_medical_icon_sm'},
          listIcon: {uri: 'mt_medical_icon_purple'},
          fields: [
            {
              dataId: 'medicalMedicineType',
              label: 'Type',
              fieldType: 'select',
              fieldValuesQuery: 'medicineTypes',
              displayRule: 'medicalAllowFreeEntry',
            },
            {
              dataId: 'medicalMedicationName',
              label: 'Medication Name',
              fieldType: 'string',
              displayRule: 'medicalAllowFreeEntry',
            },
            {
              dataId: 'medicalDoctorName',
              label: 'Doctor Name',
              fieldType: 'string',
              displayRule: 'medicalAllowFreeEntry',
            },
            {
              dataId: 'medicalMedicationAmount',
              label: 'Doctor Name',
              fieldType: 'string',
              displayRule: 'medicalAllowFreeEntry',
            },
            {
              dataId: 'medicalMedicationId',
              label: 'Medication',
              fieldType: 'select',
              fieldValuesQuery: 'medications',
              customization: 'moments/medical/useProfileMedications',
              editLocked: true,
              savedValue: 'medicalMedication',
              savedValueLabel: 'medicationDescription',
              validation: [
                {mandatory: true, message: 'Please select a Medication'},
              ],
            },
            {
              dataId: 'medicalAdministeredBy',
              label: 'Administered By',
              fieldType: 'select',
              fieldValuesQuery: 'staffPeople',
              editLocked: true,
              savedValue: 'medicalAdministeredByPerson',
              savedValueLabel: 'fullName',
              validation: [
                {mandatory: true, message: 'Please select a Administered By'},
              ],
            },
          ],
          name: 'Medical',
          type: 'medical',
          adminOnly: false,
        },
        {
          prettyName: 'Mood',
          icon: {uri: 'mt_mood_icon_sm'},
          listIcon: {uri: 'mt_mood_icon_purple'},
          fields: [
            {
              dataId: 'moodLevel',
              label: 'Mood',
              fieldType: 'buttons',
              fieldValues: [
                {fieldValue: 'Happy', fieldIcon: 'smile-o'},
                {fieldValue: 'SoSo', fieldIcon: 'meh-o'},
                {fieldValue: 'Sad', fieldIcon: 'frown-o'},
              ],
            },
          ],
          name: 'Mood',
          type: 'mood',
          adminOnly: false,
        },
        {
          prettyName: 'Incident',
          icon: {uri: 'mt_incident_icon_sm'},
          listIcon: {uri: 'mt_incident_icon_purple'},
          fields: [
            {
              dataId: 'incidentNature',
              label: 'Nature of Incident',
              fieldType: 'string',
              customization: 'moments/incident/extraIncidentFields',
            },
            {
              dataId: 'incidentActionTaken',
              label: 'Action Taken',
              fieldType: 'string',
              customization: 'moments/incident/extraIncidentFields',
            },
            {
              dataId: 'incidentLocation',
              label: 'Incident Location',
              fieldType: 'string',
              customization: 'moments/incident/extraIncidentFields',
            },
          ],
          name: 'Incident',
          type: 'incident',
          adminOnly: false,
        },
        {
          prettyName: 'Notification',
          icon: {uri: 'mt_notification_icon_sm'},
          listIcon: {uri: 'mt_notification_icon_purple'},
          validationRules: ['alertValidation', 'smsTextValidation'],
          fields: [
            {
              dataId: 'tagOnlyCheckins',
              label: 'Include only if checked in now',
              fieldType: 'checkbox',
            },
            {
              dataId: 'tagEntireOrg',
              label: 'Tag everyone (admins, staff, families)',
              fieldType: 'checkbox',
            },
            {
              dataId: 'alertSendTypeEmail',
              label: 'Send via Email',
              fieldType: 'checkbox',
            },
            {
              dataId: 'alertSendTypeText',
              label: 'Send via SMS (text)',
              fieldType: 'checkbox',
            },
            {
              dataId: 'alertSendTypePush',
              label: 'Send via Push (app)',
              fieldType: 'checkbox',
            },
          ],
          name: 'Notification',
          type: 'alert',
          adminOnly: false,
        },
        {
          prettyName: 'Supplies',
          icon: {uri: 'mt_supplies_icon_sm'},
          listIcon: {uri: 'mt_supplies_icon_purple'},
          fields: [
            {
              dataId: 'supplyType',
              label: 'Type',
              fieldType: 'select',
              fieldValuesQuery: 'supplyTypes',
            },
          ],
          name: 'Supplies',
          type: 'supplies',
          adminOnly: false,
        },
        {
          prettyName: 'Learning',
          icon: {uri: 'mt_learning_icon_sm'},
          listIcon: {uri: 'mt_learning_icon_purple'},
          fields: [
            {
              dataId: 'learningCurriculumId',
              label: 'Curriculum',
              fieldType: 'select',
              fieldValuesQuery: 'curriculums',
            },
            {
              dataId: 'learningType',
              label: 'Type',
              fieldType: 'select',
              fieldValuesQuery: 'curriculumTypes',
            },
          ],
          name: 'Learning',
          type: 'learning',
          adminOnly: false,
        },
        {
          prettyName: 'Illness',
          icon: {uri: 'mt_illness_icon_sm'},
          listIcon: {uri: 'mt_illness_icon_purple'},
          fields: [
            {
              dataId: 'illnessSymptoms',
              label: 'Symptoms Observed',
              fieldType: 'select',
              fieldValuesQuery: 'illnessSymptoms',
            },
          ],
          name: 'Illness',
          type: 'illness',
          adminOnly: false,
        },
        {
          prettyName: 'Ouch',
          icon: {uri: 'mt_ouch_icon_sm'},
          listIcon: {uri: 'mt_ouch_icon_purple'},
          fields: [
            {
              dataId: 'ouchDescription',
              label: 'Description',
              fieldType: 'text',
            },
            {dataId: 'ouchCare', label: 'Care Provided', fieldType: 'string'},
            {
              dataId: 'ouchContactedParent',
              label: 'Contacted Parent',
              fieldType: 'buttons',
              fieldValues: ['Phone', 'Email', 'In Person', 'Did Not Notify'],
            },
            {
              dataId: 'ouchCalledParentTime',
              label: 'Time',
              fieldType: 'timePicker',
            },
            {
              dataId: 'ouchContactedDoctor',
              label: 'Contacted Doctor',
              fieldType: 'buttons',
              fieldValues: ['Phone', 'Email', 'In Person', 'Did Not Notify'],
            },
            {
              dataId: 'ouchCalledDoctorTime',
              label: 'Time',
              fieldType: 'timePicker',
            },
            {
              dataId: 'ouchNurseNotified',
              label: 'Nurse Notified',
              fieldType: 'checkbox',
            },
            {
              dataId: 'ouchProfessionalMedication',
              label: 'Professional Medication Necessary?',
              fieldType: 'checkbox',
            },
          ],
          name: 'Ouch',
          type: 'ouch',
          adminOnly: false,
        },
        {
          prettyName: 'Portfolio',
          icon: {uri: 'mt_portfolio_icon_sm'},
          listIcon: {uri: 'mt_portfolio_icon_purple'},
          fields: [
            {
              dataId: 'SelectMilestoneId',
              label: 'Select a milestone',
              fieldType: 'checkbox',
            },
            {
              dataId: 'portfolioAgeRangeId',
              savedValue: 'portfolioCurriculum',
              savedValueLabel: 'headline',
              label: 'Age Range',
              fieldType: 'select',
              fieldValuesQuery: 'portfolioAgeRange',
              editLocked: false,
            },
            {
              dataId: 'portfolioCurriculumId',
              savedValue: 'portfolioCurriculum',
              savedValueLabel: 'headline',
              label: 'Activity',
              fieldType: 'select',
              fieldValuesQuery: 'curriculums',
              editLocked: false,
            },
            {
              dataId: 'portfolioAssessments',
              label: 'Observations',
              fieldType: 'assessments',
            },
          ],
          name: 'Portfolio',
          type: 'portfolio',
          adminOnly: false,
        },
        {
          prettyName: 'Behavior',
          icon: {uri: 'mt_behavior_icon_sm'},
          listIcon: {uri: 'mt_behavior_icon_purple'},
          fields: [
            {
              fieldType: 'select',
              label: 'I followed directions',
              allowBlankOption: false,
              multi: false,
              dataId: 'followedDirections',
              dataSource: 'list',
              fieldValues: [
                {
                  fieldValue: 'rewardedBehavior',
                  fieldValueLabel: 'Rewarded Behavior',
                },
                {
                  fieldValue: 'alloftheTime',
                  fieldValueLabel: 'All of the time',
                },
                {
                  fieldValue: 'mostoftheTime',
                  fieldValueLabel: 'Most of the time',
                },
                {
                  fieldValue: 'partoftheTime',
                  fieldValueLabel: 'Part of the time',
                },
                {fieldValue: 'veryLittle', fieldValueLabel: 'Very little'},
                {fieldValue: 'notatAll', fieldValueLabel: 'Not At All'},
              ],
            },
          ],
          name: 'Behavior',
          type: 'behaviorChild',
          isDynamic: false,
          adminOnly: false,
        },
        {
          prettyName: 'Performing Art',
          icon: {uri: 'mt_behavior_icon_sm'},
          listIcon: {uri: 'mt_behavior_icon_purple'},
          fields: [
            {
              fieldType: 'buttons',
              label: 'Type',
              dataId: 'performingArt',
              fieldValues: [
                {fieldValue: 'Dance'},
                {fieldValue: 'Music'},
                {fieldValue: 'Theater'},
              ],
            },
          ],
          name: 'Performing Art',
          type: 'bkPerformingArts',
          isDynamic: false,
          adminOnly: false,
        },
      ];
    }
  };
});
jest.mock('react-native-image-crop-picker', () => {
  return {
    openCamera: jest.fn(() => Promise.resolve({path: 'test.jpg'})),
    openPicker: jest.fn(() => Promise.resolve({path: 'test.jpg'})),
  };
});
jest.mock(
  '../../../app/screens/MomentEntry/components/MomentAttribution.js',
  () => {
    const props = {
      userPerson: {},
      attributionPerson: {},
      onPress: jest.fn(),
    };
    return () => (
      <mockMomentAttribution {...props}>
        <mockText>Attribution</mockText>
      </mockMomentAttribution>
    );
  },
);
const mockProps = {
  navigation: {
    dispatch: jest.fn(),
    navigate: jest.fn(),
  },
  route: {
    params: {
      newMomentType: 'other',
      tabIndex: 0,
    },
  },
  currentUser: {
    _id: 'user1',
    orgId: 'org1',
    personId: 'person1',
    type: 'admin',
  },
  currentOrg: {
    hasCustomization: jest.fn(() => false),
    getTimezone: jest.fn(() => 'America/New_York'),
    availableDynamicMomentTypes: jest.fn(() =>
      Object.keys(availableMomentTypes),
    ),
    availableMomentTypes: jest.fn(() => Object.keys(availableMomentTypes)),
    customizationExists: jest.fn(() => true),
    translate: jest.fn(),
    getAvailableAssessmentLevels: jest.fn(),
  },
};
Meteor.default = jest.fn().mockImplementation(() => {
  return {
    collection: jest.fn(),
  };
})
describe('MomentEntry Component', () => {
  beforeEach(() => {
    const mockPeopleCollection = {
      find: jest.fn().mockReturnValue([
        {
          _id: '1',
          firstName: 'John',
          lastName: 'Doe',
          type: 'person',
          checkedIn: true,
        },
        {
          _id: '2',
          firstName: 'Jane',
          lastName: 'Smith',
          type: 'person',
          checkedIn: true,
        },
      ]),
      findOne: jest.fn().mockImplementation(id => {
        if (id === '1') {
          return {
            _id: '1',
            firstName: 'John',
            lastName: 'Doe',
            type: 'staff',
            checkedIn: true,
            findCheckedInGroup: jest.fn(),
          };
        } else {
          return {
            _id: '2',
            firstName: 'Jane',
            lastName: 'Smith',
            type: 'admin',
            checkedIn: true,
            findCheckedInGroup: jest.fn(),
          };
        }
      }),
    };
    const mockGroupCollection = {
      find: jest.fn().mockReturnValue([
        {_id: '1', name: 'Group 1'},
        {_id: '2', name: 'Group 2'},
      ]),
    };
    Meteor.collection = jest.fn(collectionName => {
      if (collectionName === 'people') {
        return mockPeopleCollection;
      } else if (collectionName === 'groups') {
        return mockGroupCollection;
      } else {
        return null;
      }
    });
  });
  it('renders correctly', async () => {
    render(<MomentEntry {...mockProps} />);
    await expect(screen.getByTestId('momententry-modal')).toBeTruthy();
    await expect(screen.getByText('Comment')).toBeTruthy();
    await expect(screen.getByText('Activity')).toBeTruthy();
    await expect(screen.getByText('Food')).toBeTruthy();
    await expect(screen.getByText('Illness')).toBeTruthy();
    await expect(screen.getByText('Incident')).toBeTruthy();
  });

  it('handles moment type selection', async () => {
    const user = userEvent.setup();
    render(<MomentEntry {...mockProps} />);

    const toggleButton = await screen.findByText('Comment');
    await user.press(toggleButton);
    expect(screen.getByText('Comment Moment')).toBeTruthy();
    expect(screen.getByText('Tag')).toBeTruthy();
    expect(screen.getByText('Date')).toBeTruthy();
    expect(screen.getByText('Time')).toBeTruthy();
  });

  it('handles tag selection', async () => {
    const user = userEvent.setup();
    render(<MomentEntry {...mockProps} />);
    const toggleButton = await screen.findByText('Comment');
    await user.press(toggleButton);
    const tagButton = await screen.getByText('+Another Person');
    await user.press(tagButton);

    expect(mockProps.navigation.navigate).toHaveBeenCalledWith(
      'MomentEntryMomentTagger',
      expect.any(Object),
    );
  });

  it('handles comment input', async () => {
    render(<MomentEntry {...mockProps} />);
    const user = userEvent.setup();
    const toggleButton = await screen.findByText('Comment');
    await user.press(toggleButton);
    const commentField = await screen.findByTestId('comment-input');
    await user.type(commentField, 'Test comment');

    await expect(commentField).toHaveDisplayValue('Test comment');
  });

  it('handles save action', async () => {
    jest.spyOn(Alert, 'alert');
    render(<MomentEntry {...mockProps} />);
    const user = userEvent.setup();
    const toggleButton = await screen.findByText('Comment');
    await user.press(toggleButton);
    const saveButton = await screen.getByText('Save');
    await user.press(saveButton);

    await expect(Alert.alert).toHaveBeenCalledWith(
      'You must tag at least one person to save the moment.',
    );
  });

  it('renders date-time picker correctly', async () => {
    const momentProps = {
      ...mockProps,
      route: {
        params: {
          tabIndex: 0,
          editMoment: {
            _id: '1234',
            createdBy: '1',
            createdByPersonId: '1',
            orgId: 'org1',
            owner: '1',
            momentType: 'comment',
            momentTypePretty: 'Comment',
            time: '9:23 am',
            date: '10/07/2024',
            selectedPayTypeId: 'standard',
            comment: 'Testing Comment',
            taggedPeople: ['1'],
          },
        },
      },
    };

    render(<MomentEntry {...momentProps} />);
    const user = userEvent.setup();
    const timeField = await screen.findByTestId('time-field');
    await user.press(timeField);
    expect(screen.getByTestId('time-picker')).toBeTruthy();
  });

  it('prohibits editing staff check-in', async () => {
    const checkInProps = {
      ...mockProps,
      route: {
        params: {
          tabIndex: 0,
          editMoment: {
            _id: '1234',
            createdBy: '1',
            createdByPersonId: '1',
            orgId: 'org1',
            owner: '1',
            momentType: 'checkin',
            momentTypePretty: 'Check In',
            time: '9:23 am',
            date: '10/07/2024',
            selectedPayTypeId: 'standard',
            comment: 'Testing Check-in',
            taggedPeople: ['1'],
          },
        },
      },
    };

    render(<MomentEntry {...checkInProps} />);
    const user = userEvent.setup();
    const commentInput = await screen.findByTestId('comment-input');

    // Verify the component renders with the correct title and comment
    await expect(screen.getByText('Check In Moment')).toBeTruthy();
    await expect(commentInput).toHaveDisplayValue('Testing Check-in');

    await waitFor(() => {
      expect(screen.getByText('Comment')).toBeTruthy();
    });
  });

  it('prohibits editing admin check-in', async () => {
    const checkInProps = {
      ...mockProps,
      route: {
        params: {
          tabIndex: 0,
          editMoment: {
            _id: '1234',
            createdBy: '1',
            createdByPersonId: '1',
            orgId: 'org1',
            owner: '1',
            momentType: 'checkin',
            momentTypePretty: 'Check In',
            time: '9:23 am',
            date: '10/07/2024',
            selectedPayTypeId: 'standard',
            comment: 'Testing Check-in',
            taggedPeople: ['2'],
          },
        },
      },
    };

    render(<MomentEntry {...checkInProps} />);
    const user = userEvent.setup();
    const commentInput = await screen.findByTestId('comment-input');

    await expect(screen.getByText('Check In Moment')).toBeTruthy();
    await expect(commentInput).toHaveDisplayValue('Testing Check-in');

    expect(screen.getByText('Comment')).toBeTruthy();
  });



  describe('Tagging Logic and _prepareTaggingData effects', () => {
    const baseMockProps = {
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      currentUser: {
        _id: 'user1',
        orgId: 'org1',
        personId: 'person1',
        type: 'admin',
      },
      currentOrg: {
        ...mockProps.currentOrg, // Reuse existing mock functions
        hasCustomization: jest.fn(() => false),
        getTimezone: jest.fn(() => 'America/New_York'),
      },
    };

    let mockPeopleCollection;
    let mockGroupCollection;

    beforeEach(() => {
      // Setup fresh mocks for each test in this describe block
      // to avoid interference between tests and with the global beforeEach.
      mockPeopleCollection = {
        find: jest.fn().mockReturnValue([]), // Default to empty, can be overridden
        findOne: jest.fn(id => { // More specific mock for this describe block
          if (id === 'personToTag1') {
            return { _id: 'personToTag1', firstName: 'Alice', lastName: 'Wonder', type: 'person', checkedIn: true, findCheckedInGroup: jest.fn() };
          }
          if (id === 'personToTag2') {
            return { _id: 'personToTag2', firstName: 'Bob', lastName: 'Builder', type: 'person', checkedIn: true, findCheckedInGroup: jest.fn() };
          }
          // Fallback for other IDs if needed, or return null
          return null;
        }),
      };
      mockGroupCollection = {
        find: jest.fn().mockReturnValue([ // Provide some default groups
          { _id: 'groupToTag1', name: 'Cool Group' },
          { _id: 'groupToTag2', name: 'Awesome Team' },
        ]),
      };
      Meteor.collection = jest.fn(collectionName => {
        if (collectionName === 'people') {
          return mockPeopleCollection;
        } else if (collectionName === 'groups') {
          return mockGroupCollection;
        }
        return { find: jest.fn().mockReturnValue([]), findOne: jest.fn() }; // Default for other collections
      });
    });

    it('displays a generated label from tagged people when no route param label is provided', async () => {
      const testProps = {
        ...baseMockProps,
        route: {
          params: {
            newMomentType: 'comment', // Directly show moment content
            taggedPeople: ['personToTag1', 'group|groupToTag1'],
            tabIndex: 0,
          },
        },
      };
      render(<MomentEntry {...testProps} />);
      // The MomentPicker for 'other' type might show up first if newMomentType: 'other'
      // For 'comment', it should directly render the main view.

      // The Tag component renders the label. We search for the composite label.
      expect(await screen.findByText('Alice Wonder, Cool Group')).toBeTruthy();
    });

    it('displays the label from route.params.taggedPeopleLabel when provided, overriding generated label', async () => {
      const testProps = {
        ...baseMockProps,
        route: {
          params: {
            newMomentType: 'comment',
            taggedPeople: ['personToTag1'], // Data that would generate "Alice Wonder"
            taggedPeopleLabel: 'Custom Label From Route Params', // Override
            tabIndex: 0,
          },
        },
      };
      render(<MomentEntry {...testProps} />);
      expect(await screen.findByText('Custom Label From Route Params')).toBeTruthy();
      // Ensure the generated label is not shown
      expect(screen.queryByText('Alice Wonder')).toBeNull();
    });

    it('displays the default "+Another Person" label when no tags and no route param label', async () => {
      const testProps = {
        ...baseMockProps,
        route: {
          params: {
            newMomentType: 'comment',
            taggedPeople: [], // No tagged people
            // No taggedPeopleLabel in params
            tabIndex: 0,
          },
        },
      };
      render(<MomentEntry {...testProps} />);
      expect(await screen.findByText('+Another Person')).toBeTruthy();
    });
     it('displays a generated label including a person and multiple groups', async () => {
      const testProps = {
        ...baseMockProps,
        route: {
          params: {
            newMomentType: 'comment',
            taggedPeople: ['personToTag2', 'group|groupToTag1', 'group|groupToTag2'],
            tabIndex: 0,
          },
        },
      };
      render(<MomentEntry {...testProps} />);
      expect(await screen.findByText('Bob Builder, Cool Group, Awesome Team')).toBeTruthy();
    });

    it('handles taggedPeople being an array of objects (person selectors)', async () => {
      // This scenario assumes People.findOne can handle object selectors if tpid is an object
      // The current _prepareTaggingData passes `tpid` directly to `People.findOne` if it's an object.
      mockPeopleCollection.findOne.mockImplementation(selector => {
        if (typeof selector === 'object' && selector._id === 'personToTag1_obj') {
          return { _id: 'personToTag1_obj', firstName: 'Carol', lastName: 'Danvers', type: 'person', checkedIn: true, findCheckedInGroup: jest.fn() };
        }
        return null;
      });

      const testProps = {
        ...baseMockProps,
        route: {
          params: {
            newMomentType: 'comment',
            taggedPeople: [{ _id: 'personToTag1_obj' }, 'group|groupToTag2'],
            tabIndex: 0,
          },
        },
      };
      render(<MomentEntry {...testProps} />);
      expect(await screen.findByText('Carol Danvers, Awesome Team')).toBeTruthy();
    });
  });
});
